using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

using System.Net;
using System.Net.Sockets;
using System.Threading;

namespace WindowsFormsApp1
{
    public partial class Form1 : Form
    {
        private TcpListener _listener;
        private Thread _listenThread;
        private int _dataId = 1;
        private int _receiveCount = 0;
        private bool _listening = false;

        // 监听端口

        public Form1()
        {
            InitializeComponent();
            // 按钮点击启动监听
            btnStart.Click += BtnStart_Click;
            btnStop.Click += BtnStop_Click;
            InitDataGridView(); // 初始化表格

        }
        private void BtnStart_Click(object sender, EventArgs e)
        {
            if (!int.TryParse(txtPort.Text.Trim(), out int port) || port < 1 || port > 65535)
            {
                MessageBox.Show("请输入有效的端口号（1~65535）", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            btnStart.Enabled = false;
            btnStop.Enabled = true;
            _listening = true;

            StartListening(port);
        }
        private void BtnStop_Click(object sender, EventArgs e)
        {
            _listening = false;

            try
            {
                _listener?.Stop();
                AppendText("监听已停止\n");
            }
            catch (Exception ex)
            {
                AppendText($"停止监听异常：{ex.Message}\n");
            }

            btnStart.Enabled = true;
            btnStop.Enabled = false;
        }



        private void InitDataGridView()
        {
            dataGridView1.ColumnCount = 4;
            dataGridView1.Columns[0].Name = "标签";
            dataGridView1.Columns[1].Name = "读数据";
            dataGridView1.Columns[2].Name = "次数";
            dataGridView1.Columns[3].Name = "时间";

            dataGridView1.Columns[0].Width = 90;
            dataGridView1.Columns[1].Width = 250;
            dataGridView1.Columns[2].Width = 60;
            dataGridView1.Columns[3].Width = 170;
        }

        // 启动后台监听
        private void StartListening(int port)
        {
            try
            {
                _listener = new TcpListener(IPAddress.Any, port);
                _listener.Start();

                _listenThread = new Thread(ListenForClients)
                {
                    IsBackground = true
                };
                _listenThread.Start();

                AppendText($"[INFO] 监听已启动，端口 {port}\n");
            }
            catch (Exception ex)
            {
                AppendText($"[ERROR] 启动监听失败：{ex.Message}\n");
            }
        }
        // 循环接受客户端连接
        private void ListenForClients()
        {
            try
            {
                while (_listening)
                {
                    TcpClient client = _listener.AcceptTcpClient();
                    AppendText($"客户端已连接：{client.Client.RemoteEndPoint}\n");

                    var clientThread = new Thread(HandleClientComm) { IsBackground = true };
                    clientThread.Start(client);
                }
            }
            catch (SocketException ex)
            {
                if (_listening)
                    AppendText($"监听异常：{ex.Message}\n");
                else
                    AppendText("监听已停止，线程退出。\n");
            }
        }


        // 处理单个客户端的数据接收
        private void HandleClientComm(object clientObj)
        {
            var client = clientObj as TcpClient;
            var stream = client.GetStream();
            var buffer = new byte[1024];
            int bytesRead;

            try
            {
                while ((bytesRead = stream.Read(buffer, 0, buffer.Length)) > 0)
                {
                    // 转成十六进制字符串
                    if (bytesRead >= 8)
                    {
                        // 提取最后8个字节
                        byte[] last8Bytes = new byte[8];
                        Array.Copy(buffer, bytesRead - 8, last8Bytes, 0, 8);

                        string hex = BitConverter.ToString(last8Bytes).Replace("-", " ");

                        // 记录并显示 ID 和接收次数
                        int id = _dataId++;
                        int count = ++_receiveCount;
                        string timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                        dataGridView1.Invoke((MethodInvoker)(() =>
                        {
                            dataGridView1.Rows.Add(id, hex, count, timestamp);
                        }));
                      
                    }
                    else
                    {
                        AppendText($"[RECV] 数据不足8字节，忽略\n");
                    }

                }
            }
            catch (Exception ex)
            {
                AppendText($"[ERROR] 接收异常：{ex.Message}\n");
            }
            finally
            {
                AppendText($"[INFO] 客户端已断开：{client.Client.RemoteEndPoint}\n");
                client.Close();
            }
        }

        // 安全地在 UI 线程追加文本
        private void AppendText(string text)
        {
        }

        // 窗体关闭时，释放资源
        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            base.OnFormClosing(e);
            _listener?.Stop();
            _listenThread?.Abort();
        }

        private void Form1_Load(object sender, EventArgs e)
        {

        }

        private void richTextBox1_TextChanged(object sender, EventArgs e)
        {

        }

        private void label1_Click(object sender, EventArgs e)
        {

        }

        private void button1_Click(object sender, EventArgs e)
        {

        }
    }
}
