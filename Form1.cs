using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

using System.Net;
using System.Net.Sockets;
using System.Threading;
using System.Linq.Expressions;

namespace WindowsFormsApp1
{
    public partial class Form1 : Form
    {
        private TcpClient _client;
        private Thread _listenThread;
        private int _dataId = 1;
        private int _receiveCount = 0;
        private bool _connected = false;

        // 目标服务器信息
        private const string SERVER_IP = "*************";
        private const int SERVER_PORT = 502;

        public Form1()
        {
            InitializeComponent();
            // 按钮点击启动监听
            btnStart.Click += BtnStart_Click;
            btnStop.Click += BtnStop_Click;
            btnClear.Click += BtnClear_Click;
            InitDataGridView(); // 初始化表格

        }
        //连接服务器按钮事情
        private void BtnStart_Click(object sender, EventArgs e)
        {
            btnStart.Enabled = false;
            btnStop.Enabled = true;
            _connected = true;

            ConnectToServer();
        }
        //暂停连接服务器按钮事件
        private void BtnStop_Click(object sender, EventArgs e)
        {
            _connected = false;

            try
            {
                _client?.Close();
                AppendText("连接已断开\n");
            }
            catch (Exception ex)
            {
                AppendText($"断开连接异常：{ex.Message}\n");
            }

            btnStart.Enabled = true;
            btnStop.Enabled = false;
        }
        //清空当前数据列表按钮事情
        private void BtnClear_Click(object sender, EventArgs e)
        {
            try{
                if (dataGridView1.Rows.Count > 0)
            {
                dataGridView1.Rows.Clear();
                AppendText("数据已清空\n");
                _dataId = 1;
                _receiveCount = 0;
            }
            }
            catch(Exception ex)
            {
                AppendText($"清空数据异常：{ex.Message}\n");
            }
        }

        // 初始化标签列表
        private void InitDataGridView()
        {
            dataGridView1.ColumnCount = 4;
            dataGridView1.Columns[0].Name = "标签";
            dataGridView1.Columns[1].Name = "读数据";
            dataGridView1.Columns[2].Name = "次数";
            dataGridView1.Columns[3].Name = "时间";

            dataGridView1.Columns[0].Width = 90;
            dataGridView1.Columns[1].Width = 250;
            dataGridView1.Columns[2].Width = 60;
            dataGridView1.Columns[3].Width = 170;
        }

        // 连接到服务器
        private void ConnectToServer()
        {
            try
            {
                _client = new TcpClient();
                _client.Connect(SERVER_IP, SERVER_PORT);

                // 连接成功提示
                AppendText($"[INFO] 连接成功！服务器地址：{SERVER_IP}:{SERVER_PORT}\n");
                MessageBox.Show($"连接成功！\n服务器地址：{SERVER_IP}:{SERVER_PORT}", "连接成功", MessageBoxButtons.OK, MessageBoxIcon.Information);

                _listenThread = new Thread(ReceiveData)
                {
                    IsBackground = true
                };
                _listenThread.Start();

                AppendText($"[INFO] 开始监听数据...\n");
            }
            catch (Exception ex)
            {
                AppendText($"[ERROR] 连接失败：{ex.Message}\n");
                MessageBox.Show($"连接失败：{ex.Message}", "连接错误", MessageBoxButtons.OK, MessageBoxIcon.Error);

                // 连接失败时恢复按钮状态
                btnStart.Enabled = true;
                btnStop.Enabled = false;
                _connected = false;
            }
        }
        // 接收服务器数据
        private void ReceiveData()
        {
            try
            {
                var stream = _client.GetStream();
                var buffer = new byte[1024];
                int bytesRead;

                while (_connected && (bytesRead = stream.Read(buffer, 0, buffer.Length)) > 0)
                {
                    // 转成十六进制字符串
                    if (bytesRead >= 8)
                    {
                        // 提取最后8个字节
                        byte[] last8Bytes = new byte[8];
                        Array.Copy(buffer, bytesRead - 8, last8Bytes, 0, 8);

                        string hex = BitConverter.ToString(last8Bytes).Replace("-", " ");

                        // 记录并显示 ID 和接收次数
                        int id = _dataId++;
                        int count = ++_receiveCount;
                        string timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                        dataGridView1.Invoke((MethodInvoker)(() =>
                        {
                            dataGridView1.Rows.Add(id, hex, count, timestamp);
                        }));
                    }
                    else
                    {
                        AppendText($"[RECV] 数据不足8字节，忽略\n");
                    }
                }
            }
            catch (Exception ex)
            {
                if (_connected)
                    AppendText($"[ERROR] 接收数据异常：{ex.Message}\n");
                else
                    AppendText("[INFO] 数据接收线程已停止\n");
            }
            finally
            {
                AppendText($"[INFO] 与服务器的连接已断开\n");
                _client?.Close();
            }
        }




        // 安全地在 UI 线程追加文本
        private void AppendText(string text)
        {
            // 输出到控制台用于调试
            System.Diagnostics.Debug.WriteLine(text);
            Console.WriteLine(text);
        }

        // 窗体关闭时，释放资源
        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            base.OnFormClosing(e);
            _connected = false;
            _client?.Close();
            _listenThread?.Abort();
        }

        private void Form1_Load(object sender, EventArgs e)
        {

        }

        private void richTextBox1_TextChanged(object sender, EventArgs e)
        {

        }

        private void label1_Click(object sender, EventArgs e)
        {

        }

        private void button1_Click(object sender, EventArgs e)
        {

        }
    }
}
